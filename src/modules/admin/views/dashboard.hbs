<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Marketing Automation Dashboard</h1>
        <p>Welcome back, <strong>{{user.username}}</strong>!</p>
    </div>

    <div class="dashboard-content">
        <div class="simple-card">
            <h3>System Status</h3>
            <p class="status-ok">✅ Authentication System Working</p>
            <p class="status-ok">✅ Admin Interface Active</p>
            <p class="status-ok">✅ Database Connected</p>
            <p class="status-ok">✅ Email Service Ready</p>
        </div>

        {{!-- ✅ ADD: Pipeline System Overview --}}
        <div class="simple-card">
            <h3>Pipeline System</h3>
            <div class="pipeline-overview">
                <div class="pipeline-stat">
                    <span class="stat-number">2</span>
                    <span class="stat-label">Active Pipelines</span>
                </div>
                <div class="pipeline-stat">
                    <span class="stat-number">0</span>
                    <span class="stat-label">Running Now</span>
                </div>
                <div class="pipeline-stat">
                    <span class="stat-number">0</span>
                    <span class="stat-label">Pending Review</span>
                </div>
            </div>
            <div class="pipeline-actions">
                <a href="/api/pipeline/dashboard" class="btn btn-secondary">View Pipeline Dashboard</a>
            </div>
        </div>

        <div class="simple-card">
            <h3>Quick Actions</h3>
            <div class="action-buttons">
                <a href="/admin/import-data" class="btn btn-secondary">Import Customer Data</a>
                <a href="/api/templates" class="btn btn-secondary">Manage Templates</a>
            </div>
        </div>

        <div class="simple-card">
            <h3>System Information</h3>
            <p>Marketing automation system with pipeline-based email campaigns.</p>
            <p>Features: Customer management, email templates, automated pipelines, AI content generation.</p>
        </div>
    </div>
</div>

<style>
    .dashboard-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
    }

    .dashboard-header {
        text-align: center;
        margin-bottom: 2rem;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
    }

    .dashboard-header h1 {
        margin: 0 0 0.5rem 0;
        font-size: 2rem;
    }

    .dashboard-content {
        display: grid;
        gap: 1.5rem;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .simple-card {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }

    .simple-card h3 {
        margin-top: 0;
        color: #343a40;
        margin-bottom: 1rem;
    }

    .status-ok {
        color: #28a745;
        margin: 0.5rem 0;
        font-weight: 500;
    }

    /* ✅ ADD: Pipeline overview styles */
    .pipeline-overview {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .pipeline-stat {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #dee2e6;
    }

    .stat-number {
        display: block;
        font-size: 1.5rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .pipeline-actions {
        text-align: center;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.75rem 1rem;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        color: white;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 1rem;
        }

        .dashboard-content {
            grid-template-columns: 1fr;
        }

        .pipeline-overview {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            flex-direction: column;
        }

        .btn {
            width: 100%;
        }
    }
</style>